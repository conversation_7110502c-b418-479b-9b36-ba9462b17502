<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('acs_commission_distributions', function (Blueprint $table) {
            $table->boolean('is_default_override')->default(false)->after('agent_commission_amount');
            $table->decimal('original_main_agent_percentage', 10, 2)->nullable()->after('is_default_override');
            $table->decimal('original_agent_percentage', 10, 2)->nullable()->after('original_main_agent_percentage');
            $table->text('override_reason')->nullable()->after('original_agent_percentage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('acs_commission_distributions', function (Blueprint $table) {
            $table->dropColumn([
                'is_default_override',
                'original_main_agent_percentage',
                'original_agent_percentage',
                'override_reason'
            ]);
        });
    }
};
